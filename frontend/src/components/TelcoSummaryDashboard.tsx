import React, { useState } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Badge, Spinner } from 'react-bootstrap';
import axios from 'axios';

const baseURL = import.meta.env.DEV
  ? 'http://localhost:8080'
  : import.meta.env.VITE_API_BASE_URL;

interface TelcoSummaryResult {
  target_date: string;
  hisa_summary: {
    [telco: string]: {
      total_transactions: number;
      total_value: string;
      by_status: {
        [status: string]: {
          count: number;
          value: string;
        };
      };
      by_type: {
        [type: string]: {
          count: number;
          value: string;
        };
      };
    };
  };
  reconciliation_results: {
    [telco: string]: {
      success?: boolean;
      data?: any;
      error?: string;
    };
  };
}

interface TelcoSummaryDashboardProps {
  token: string;
}

const TelcoSummaryDashboard: React.FC<TelcoSummaryDashboardProps> = ({ token }) => {
  const [targetDate, setTargetDate] = useState<string>('');

  // Telco files
  const [mtnFile, setMtnFile] = useState<File | null>(null);
  const [mtnFile2, setMtnFile2] = useState<File | null>(null);
  const [airtelFile, setAirtelFile] = useState<File | null>(null);
  const [airtelFile2, setAirtelFile2] = useState<File | null>(null);
  const [gloFile, setGloFile] = useState<File | null>(null);
  const [gloFile2, setGloFile2] = useState<File | null>(null);

  const [useTransactionId, setUseTransactionId] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [result, setResult] = useState<TelcoSummaryResult | null>(null);
  const [error, setError] = useState<string>('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!targetDate) {
      setError('Target date is required.');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    const formData = new FormData();
    formData.append('target_date', targetDate);

    // Add telco files if provided
    if (mtnFile) formData.append('mtn_file', mtnFile);
    if (mtnFile2) formData.append('mtn_file2', mtnFile2);
    if (airtelFile) formData.append('airtel_file', airtelFile);
    if (airtelFile2) formData.append('airtel_file2', airtelFile2);
    if (gloFile) formData.append('glo_file', gloFile);
    if (gloFile2) formData.append('glo_file2', gloFile2);

    formData.append('use_transaction_id', useTransactionId.toString());

    try {
      const response = await axios.post(
        `${baseURL}/telco-summary/`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          },
        }
      );

      if (response.data.status) {
        setResult(response.data.data);
      } else {
        setError(response.data.error || 'Unknown error occurred');
      }
    } catch (err) {
      console.error('Telco summary error:', err);
      if (axios.isAxiosError(err) && err.response?.data?.error) {
        setError(err.response.data.error);
      } else {
        setError('Failed to generate telco summary. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const renderTelcoSummary = (telco: string, summary: any) => {
    return (
      <Card key={telco} className="mb-4">
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">{telco}</h5>
          <Badge bg={summary.total_transactions > 0 ? 'success' : 'secondary'}>
            {summary.total_transactions} transactions
          </Badge>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <h6>By Status</h6>
              <Table size="sm" className="mb-3">
                <thead>
                  <tr>
                    <th>Status</th>
                    <th>Count</th>
                    <th>Value</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(summary.by_status).map(([status, data]: [string, any]) => (
                    <tr key={status}>
                      <td>
                        <Badge bg={status === 'SUCCESS' ? 'success' : 'danger'}>
                          {status}
                        </Badge>
                      </td>
                      <td>{data.count}</td>
                      <td>{data.value}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Col>
            <Col md={6}>
              <h6>By Type</h6>
              <Table size="sm" className="mb-3">
                <thead>
                  <tr>
                    <th>Type</th>
                    <th>Count</th>
                    <th>Value</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(summary.by_type).map(([type, data]: [string, any]) => (
                    <tr key={type}>
                      <td>
                        <Badge bg={type === 'AIRTIME' ? 'primary' : 'info'}>
                          {type}
                        </Badge>
                      </td>
                      <td>{data.count}</td>
                      <td>{data.value}</td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </Col>
          </Row>
          <div className="mt-3">
            <strong>Total Value: {summary.total_value}</strong>
          </div>
        </Card.Body>
      </Card>
    );
  };

  const renderReconciliationResults = () => {
    if (!result?.reconciliation_results || Object.keys(result.reconciliation_results).length === 0) {
      return null;
    }

    return (
      <div className="mt-4">
        <h4>Reconciliation Results</h4>
        {Object.entries(result.reconciliation_results).map(([telco, reconciliation]: [string, any]) => (
          <Card key={telco} className="mb-3">
            <Card.Header>
              <h6 className="mb-0">{telco} Reconciliation</h6>
            </Card.Header>
            <Card.Body>
              {reconciliation.error ? (
                <Alert variant="danger">
                  <strong>Error:</strong> {reconciliation.error}
                </Alert>
              ) : reconciliation.success && reconciliation.data ? (
                <div>
                  <Row>
                    <Col md={6}>
                      <p><strong>HISA Total:</strong> {reconciliation.data.hisa_total}</p>
                      <p><strong>Telco Total:</strong> {reconciliation.data.telco_total}</p>
                      <p><strong>Difference:</strong> {reconciliation.data.difference}</p>
                    </Col>
                    <Col md={6}>
                      <p><strong>HISA Success Count:</strong> {reconciliation.data.hisa_success_count}</p>
                      <p><strong>Telco Success Count:</strong> {reconciliation.data.telco_success_count}</p>
                      <p><strong>Missing in HISA:</strong> {reconciliation.data.missing_in_hisa_count}</p>
                    </Col>
                  </Row>
                </div>
              ) : (
                <Alert variant="warning">No reconciliation data available</Alert>
              )}
            </Card.Body>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <Container>
      <Form onSubmit={handleSubmit}>
        <Row>
          <Col md={6}>
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">Required Files</h5>
              </Card.Header>
              <Card.Body>
                <Form.Group className="mb-3">
                  <Form.Label>Target Date</Form.Label>
                  <Form.Control
                    type="date"
                    value={targetDate}
                    onChange={(e) => setTargetDate(e.target.value)}
                    required
                  />
                </Form.Group>

                <Alert variant="info" className="mb-3">
                  <Alert.Heading>
                    <i className="bi bi-info-circle me-2"></i>
                    HISA Transaction Logs
                  </Alert.Heading>
                  <p className="mb-0">
                    HISA transaction logs will be automatically fetched from both hisa_one and hisa_two sources for the selected date.
                    No need to upload HISA files manually.
                  </p>
                </Alert>

                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Use Transaction ID for matching"
                    checked={useTransactionId}
                    onChange={(e) => setUseTransactionId(e.target.checked)}
                  />
                </Form.Group>
              </Card.Body>
            </Card>
          </Col>

          <Col md={6}>
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">Optional Telco Files</h5>
                <small className="text-muted">Upload telco files for reconciliation (optional)</small>
              </Card.Header>
              <Card.Body>
                <h6>MTN Files</h6>
                <Form.Group className="mb-2">
                  <Form.Label>MTN File 1</Form.Label>
                  <Form.Control
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => {
                      const target = e.target as HTMLInputElement;
                      setMtnFile(target.files?.[0] || null);
                    }}
                  />
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Label>MTN File 2</Form.Label>
                  <Form.Control
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => {
                      const target = e.target as HTMLInputElement;
                      setMtnFile2(target.files?.[0] || null);
                    }}
                  />
                </Form.Group>

                <h6>Airtel Files</h6>
                <Form.Group className="mb-2">
                  <Form.Label>Airtel File 1</Form.Label>
                  <Form.Control
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => {
                      const target = e.target as HTMLInputElement;
                      setAirtelFile(target.files?.[0] || null);
                    }}
                  />
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Label>Airtel File 2</Form.Label>
                  <Form.Control
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => {
                      const target = e.target as HTMLInputElement;
                      setAirtelFile2(target.files?.[0] || null);
                    }}
                  />
                </Form.Group>

                <h6>GLO Files</h6>
                <Form.Group className="mb-2">
                  <Form.Label>GLO File 1</Form.Label>
                  <Form.Control
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => {
                      const target = e.target as HTMLInputElement;
                      setGloFile(target.files?.[0] || null);
                    }}
                  />
                </Form.Group>
                <Form.Group className="mb-3">
                  <Form.Label>GLO File 2</Form.Label>
                  <Form.Control
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={(e) => {
                      const target = e.target as HTMLInputElement;
                      setGloFile2(target.files?.[0] || null);
                    }}
                  />
                </Form.Group>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        <div className="text-center mb-4">
          <Button type="submit" variant="info" size="lg" disabled={loading}>
            {loading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Generating Summary...
              </>
            ) : (
              'Generate Telco Summary'
            )}
          </Button>
        </div>
      </Form>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {result && (
        <div>
          <h3>Telco Summary for {result.target_date}</h3>
          <div className="mb-4">
            {Object.entries(result.hisa_summary).map(([telco, summary]) =>
              renderTelcoSummary(telco, summary)
            )}
          </div>
          {renderReconciliationResults()}
        </div>
      )}
    </Container>
  );
};

export default TelcoSummaryDashboard;
