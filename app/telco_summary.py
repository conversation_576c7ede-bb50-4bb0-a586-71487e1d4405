"""
Telco Summary Service Module

This module provides functionality for generating telco summaries with or without telco files.
It follows SOLID principles for clean architecture and maintainability.

Single Responsibility Principle: Each class has a single, well-defined responsibility
Open/Closed Principle: Classes are open for extension but closed for modification
Liskov Substitution Principle: Derived classes can be substituted for their base classes
Interface Segregation Principle: Interfaces are specific to client needs
Dependency Inversion Principle: High-level modules don't depend on low-level modules
"""

import io
import os
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Union
import pandas as pd

from app import clean_merge_hisa, reconcile_logs
from app.airtel import prepare_airtel_telco
from app.glo import prepare_glo_telco
from app.mtn import prepare_mtn_telco
from app.utils import format_currency


class TelcoProcessor(ABC):
    """Abstract base class for telco data processors (Interface Segregation)"""

    @abstractmethod
    def process_telco_data(self, df: pd.DataFrame) -> Union[pd.DataFrame, str]:
        """Process telco data and return cleaned DataFrame or error message"""
        pass

    @abstractmethod
    def get_telco_name(self) -> str:
        """Get the telco name"""
        pass


class MTNProcessor(TelcoProcessor):
    """MTN telco data processor"""

    def process_telco_data(self, df: pd.DataFrame) -> Union[pd.DataFrame, str]:
        return prepare_mtn_telco(df)

    def get_telco_name(self) -> str:
        return "MTN"


class AirtelProcessor(TelcoProcessor):
    """Airtel telco data processor"""

    def process_telco_data(self, df: pd.DataFrame) -> Union[pd.DataFrame, str]:
        return prepare_airtel_telco(df)

    def get_telco_name(self) -> str:
        return "AIRTEL"


class GLOProcessor(TelcoProcessor):
    """GLO telco data processor"""

    def process_telco_data(self, df: pd.DataFrame) -> Union[pd.DataFrame, str]:
        return prepare_glo_telco(df)

    def get_telco_name(self) -> str:
        return "GLO"


class TelcoProcessorFactory:
    """Factory for creating telco processors (Dependency Inversion)"""

    _processors = {
        "MTN": MTNProcessor,
        "AIRTEL": AirtelProcessor,
        "GLO": GLOProcessor,
    }

    @classmethod
    def create_processor(cls, telco_name: str) -> TelcoProcessor:
        """Create a telco processor for the given telco name"""
        processor_class = cls._processors.get(telco_name.upper())
        if not processor_class:
            raise ValueError(f"Unsupported telco: {telco_name}")
        return processor_class()

    @classmethod
    def get_supported_telcos(cls) -> List[str]:
        """Get list of supported telco names"""
        return list(cls._processors.keys())


class HisaDataAnalyzer:
    """Analyzer for HISA data (Single Responsibility)"""

    def analyze_by_telco(
        self, hisa_df: pd.DataFrame, target_date: str
    ) -> Dict[str, Dict]:
        """Analyze HISA data by telco for the target date"""
        target_date_obj = pd.to_datetime(target_date).date()
        hisa_day = hisa_df[hisa_df["Date"].dt.date == target_date_obj].copy()

        summary = {}

        for telco in TelcoProcessorFactory.get_supported_telcos():
            telco_data = hisa_day[hisa_day["Network"].str.upper() == telco].copy()

            if telco_data.empty:
                summary[telco] = self._create_empty_summary()
                continue

            # Analyze by transaction status
            status_summary = self._analyze_by_status(telco_data)

            # Analyze by transaction type
            type_summary = self._analyze_by_type(telco_data)

            summary[telco] = {
                "total_transactions": len(telco_data),
                "total_value": format_currency(telco_data["Amount"].sum()),
                "by_status": status_summary,
                "by_type": type_summary,
            }

        return summary

    def _analyze_by_status(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Analyze data by transaction status"""
        status_summary = {}

        for status in ["SUCCESS", "FAILED"]:
            status_data = df[df["TransactionStatus"] == status]
            status_summary[status] = {
                "count": len(status_data),
                "value": format_currency(status_data["Amount"].sum()),
            }

        return status_summary

    def _analyze_by_type(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Analyze data by transaction type"""
        type_summary = {}

        for tx_type in ["AIRTIME", "DATA"]:
            type_data = df[df["TransactionType"] == tx_type]
            type_summary[tx_type] = {
                "count": len(type_data),
                "value": format_currency(type_data["Amount"].sum()),
            }

        return type_summary

    def _create_empty_summary(self) -> Dict:
        """Create empty summary for telcos with no data"""
        return {
            "total_transactions": 0,
            "total_value": format_currency(0),
            "by_status": {
                "SUCCESS": {"count": 0, "value": format_currency(0)},
                "FAILED": {"count": 0, "value": format_currency(0)},
            },
            "by_type": {
                "AIRTIME": {"count": 0, "value": format_currency(0)},
                "DATA": {"count": 0, "value": format_currency(0)},
            },
        }


class TelcoReconciliationService:
    """Service for telco reconciliation (Single Responsibility)"""

    def __init__(self):
        self.processor_factory = TelcoProcessorFactory()

    def reconcile_with_telco(
        self,
        telco_name: str,
        hisa_df: pd.DataFrame,
        telco_files: List[bytes],
        target_date: str,
        use_transaction_id: bool = False,
    ) -> Dict:
        """Reconcile HISA data with telco files"""
        try:
            processor = self.processor_factory.create_processor(telco_name)

            # Process telco files
            telco_dfs = []
            for file_content in telco_files:
                if telco_name.upper() == "AIRTEL":
                    df = pd.read_excel(io.BytesIO(file_content), dtype=str)
                else:
                    df = pd.read_excel(io.BytesIO(file_content))
                telco_dfs.append(df)

            # Combine telco files if multiple
            telco_df = pd.concat(telco_dfs) if len(telco_dfs) > 1 else telco_dfs[0]

            # Process telco data
            cleaned_telco = processor.process_telco_data(telco_df)

            if isinstance(cleaned_telco, str):
                return {"error": cleaned_telco}

            # Perform reconciliation
            result = reconcile_logs(
                mno=telco_name.upper(),
                target_date=target_date,
                hisa_admin=hisa_df,
                telco_df=cleaned_telco,
                use_transaction_id=use_transaction_id,
            )

            return {"success": True, "data": result}

        except Exception as e:
            return {"error": f"Error reconciling {telco_name}: {str(e)}"}


class TelcoSummaryService:
    """Main service for telco summary functionality (Open/Closed Principle)"""

    def __init__(self):
        self.hisa_analyzer = HisaDataAnalyzer()
        self.reconciliation_service = TelcoReconciliationService()

    def generate_summary(
        self,
        target_date: str,
        hisa_file1: bytes,
        hisa_file2: bytes,
        telco_files: Optional[Dict[str, List[bytes]]] = None,
        use_transaction_id: bool = False,
    ) -> Dict:
        """
        Generate telco summary with or without telco files

        Args:
            target_date: Target date for analysis
            hisa_file1: HISA file 1 content
            hisa_file2: HISA file 2 content
            telco_files: Optional dict of telco files {telco_name: [file_contents]}
            use_transaction_id: Whether to use transaction ID in reconciliation

        Returns:
            Dict containing summary data
        """
        try:
            # Validate date
            target_date_obj = datetime.strptime(target_date, "%Y-%m-%d").date()

            # Process HISA files - we need to process for all telcos to get complete data
            hisa_dfs = []
            for i, hisa_content in enumerate([hisa_file1, hisa_file2], 1):
                # Process each HISA file for all telcos and combine
                telco_dfs = []
                for telco in TelcoProcessorFactory.get_supported_telcos():
                    try:
                        hisa_df = clean_merge_hisa(
                            telco, f"hisa{i}", io.BytesIO(hisa_content)
                        )
                        if not hisa_df.empty:
                            telco_dfs.append(hisa_df)
                    except Exception:
                        # Continue if this telco processing fails
                        continue

                if telco_dfs:
                    # Combine all telco data from this HISA file
                    combined_file_df = pd.concat(telco_dfs, ignore_index=True)
                    hisa_dfs.append(combined_file_df)

            if not hisa_dfs:
                return {"error": "No valid HISA data found in uploaded files"}

            combined_hisa_df = (
                pd.concat(hisa_dfs, ignore_index=True)
                if len(hisa_dfs) > 1
                else hisa_dfs[0]
            )

            # Generate HISA-only summary
            hisa_summary = self.hisa_analyzer.analyze_by_telco(
                combined_hisa_df, target_date
            )

            result = {
                "target_date": target_date,
                "hisa_summary": hisa_summary,
                "reconciliation_results": {},
            }

            # If telco files provided, perform reconciliation
            if telco_files:
                for telco_name, files in telco_files.items():
                    if files:  # Only process if files are provided
                        reconciliation_result = (
                            self.reconciliation_service.reconcile_with_telco(
                                telco_name=telco_name,
                                hisa_df=combined_hisa_df,
                                telco_files=files,
                                target_date=target_date,
                                use_transaction_id=use_transaction_id,
                            )
                        )
                        result["reconciliation_results"][
                            telco_name
                        ] = reconciliation_result

            return result

        except Exception as e:
            return {"error": f"Error generating telco summary: {str(e)}"}
